# E2E Tests for Clinic Portal Web Application

This directory contains end-to-end tests for the clinic portal web application using Playwright.

## Setup

The tests are already configured and ready to run. Playwright browsers are installed globally.

## Running Tests

### From the root directory:

```bash
# Run all e2e tests
npm run e2e

# Run tests with browser UI visible
npm run e2e:headed

# Run tests with Playwright UI mode
npm run e2e:ui

# Debug tests step by step
npm run e2e:debug

# View test report
npm run e2e:report
```

### From the e2e-tests directory:

```bash
cd e2e-tests

# Run all tests
npm test

# Run tests with browser UI visible
npm run test:headed

# Run tests with Playwright UI mode
npm run test:ui

# Debug tests
npm run test:debug

# Run tests on specific browsers
npm run test:chromium
npm run test:firefox
npm run test:webkit

# Run mobile tests only
npm run test:mobile

# View test report
npm run report
```

## Test Structure

- `tests/` - Contains all test files
- `playwright.config.ts` - Playwright configuration
- `package.json` - Dependencies and scripts

## Configuration

The tests are configured to:
- Run against `http://localhost:3000` (automatically starts the dev server)
- Test on Chromium, Firefox, and WebKit browsers
- Include mobile viewport testing
- Take screenshots and videos on failure
- Generate HTML reports

## Writing Tests

Test files should be placed in the `tests/` directory and follow the naming convention `*.spec.ts`.

Example test structure:
```typescript
import { test, expect } from '@playwright/test';

test.describe('Feature Name', () => {
  test('should do something', async ({ page }) => {
    await page.goto('/');
    await expect(page).toHaveTitle(/Expected Title/);
  });
});
```

## Best Practices

1. Use descriptive test names
2. Group related tests with `test.describe()`
3. Use page object models for complex interactions
4. Wait for elements to be visible before interacting
5. Use `page.waitForLoadState('networkidle')` for dynamic content
6. Avoid hard-coded waits (`page.waitForTimeout()`)
