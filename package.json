{"name": "e2e-tests", "version": "1.0.0", "main": "index.js", "scripts": {"test": "playwright test", "test:headed": "playwright test --headed", "test:ui": "playwright test --ui", "test:debug": "playwright test --debug", "test:chromium": "playwright test --project=chromium", "test:firefox": "playwright test --project=firefox", "test:webkit": "playwright test --project=webkit", "test:mobile": "playwright test --project='Mobile Chrome' --project='Mobile Safari'", "report": "playwright show-report", "install": "playwright install"}, "keywords": ["e2e", "testing", "playwright", "clinic-portal"], "author": "", "license": "ISC", "description": "End-to-end tests for the clinic portal web application"}